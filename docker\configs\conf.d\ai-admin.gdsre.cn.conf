server {
        server_name ai-admin.gdsre.cn ai-admin2.gdsre.cn;
        listen 80;
        include /etc/nginx/conf.d/common/whlist.conf;
        #rewrite ^(.*) https://ai-admin.gdsre.cn$1 permanent;

	root /data/web/ai-admin/;

        location / {
                proxy_pass http://********:9527;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_buffering off;
        }

        #
        client_max_body_size 500M;

	#location / {
	#	rewrite ^(.*) https://ai-admin.gdsre.cn$1 permanent;
	#}

}

server {
	#listen 80;
	server_name ai-admin.gdsre.cn ai-admin2.gdsre.cn;
	client_max_body_size 500M;

	include /etc/nginx/conf.d/common/gdsre.cn-ssl.conf;
	include /etc/nginx/conf.d/common/whlist.conf;


	#fastcgi_connect_timeout 900;
	#fastcgi_send_timeout 900;
	#fastcgi_read_timeout 900;

	proxy_send_timeout 300;
	proxy_read_timeout 300;
	proxy_connect_timeout 300;

	root /data/web/ai-admin/;

	location /stablediffusion {
        	rewrite ^(.*) http://ai-admin.gdsre.cn$1 permanent;
	}

	location / {
		proxy_pass http://********:9527;
		proxy_http_version 1.1;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_set_header Host $host;
#		proxy_cache_bypass $http_upgrade;
		proxy_buffering off;
	}
	error_log /data/logs/ai-admin-web.gdsre.cn_error.log debug;
    access_log /data/logs/ai-admin-web.gdsre.cn_acess.log;
}
