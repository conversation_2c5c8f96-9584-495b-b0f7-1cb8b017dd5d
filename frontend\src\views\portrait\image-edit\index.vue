<script lang="ts" setup>
import FunctionTabs from './modules/function-tabs.vue';
import HeaderMenu from './modules/header-menu.vue';
</script>

<template>
  <NLayout>
    <NLayoutHeader class="h-16" bordered>
      <HeaderMenu />
    </NLayoutHeader>

    <NLayoutSider :width="100"  :native-scrollbar="false" class="h-full" >
      <FunctionTabs   />
    </NLayoutSider>
  </NLayout>
</template>

<style scoped></style>
