<script lang="ts" setup>
import { ref } from 'vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useThemeStore } from '@/store/modules/theme';

const themeStore = useThemeStore();

const activeKey = ref<string | null>('smart-cutout');

interface MenuItem {
  label: string;
  key: string;
  icon: string;
}

const menuItems: MenuItem[] = [
  {
    label: '智能抠图',
    key: 'smart-cutout',
    icon: 'material-symbols-light:background-replace-outline'
  },
  {
    label: '高清化',
    key: 'high-definition',
    icon: 'material-symbols-light:hd-outline'
  },
  {
    label: '无损放大',
    key: 'img-enlarge',
    icon: 'fluent:slide-size-20-regular'
  }
];

const handleItemClick = (key: string) => {
  activeKey.value = key;
};
</script>

<template>
  <NCard class="h-full" :bordered="false">
    <NFlex vertical class="menu-container">
      <div
        v-for="item in menuItems"
        :key="item.key"
        class="menu-item"
        :class="{ active: activeKey === item.key }"
        @click="handleItemClick(item.key)"
      >
        <NFlex vertical align="center" justify="center" class="menu-item-content">
          <SvgIcon :icon="item.icon" class="menu-icon text-2xl" />
          <NText class="menu-text">{{ item.label }}</NText>
        </NFlex>
      </div>
    </NFlex>
  </NCard>
</template>

<style scoped>
.menu-container {
  padding: 8px 0;
  height: 100%;
}

.menu-item {
  padding: 16px 12px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin: 2px -10px;
}

.menu-item:hover {
  background-color: v-bind('themeStore.darkMode ? "rgba(59, 59, 59, 0.3)" : "rgba(164, 164, 164, 0.15)"');
}

.menu-item.active {
  background-color: v-bind('themeStore.darkMode ? "rgba(59, 59, 59, 0.3)" : "rgba(164, 164, 164, 0.15)"');
}

.menu-item-content {
  gap: 8px;
}

.menu-text {
  font-size: 12px;
  transition: color 0.2s ease;
  text-align: center;
  white-space: nowrap;
}
</style>
